<!-- 机构信息 -->
<template>
  <div class="card-container">
    <BuseCrud
      ref="crud"
      :loading="loading"
      :filterOptions="filterOptions"
      :tablePage="tablePage"
      :pagerProps="pagerProps"
      :tableColumn="tableColumn"
      :tableData="tableData"
      :tableProps="tableProps"
      :modalConfig="modalConfig"
      @modalConfirm="modalConfirmHandler"
      @loadData="loadData"
      @rowEdit="rowEdit"
    >
      <template slot="filterCustomBtn">
        <div class="btn-wrap">
          <!-- <el-button
            type="primary"
            icon="el-icon-download"
            @click.stop="handleExport"
            v-has-permi="['serviceProvider:list:export']"
            >导出
          </el-button> -->
          <el-button
            type="primary"
            icon="el-icon-search"
            @click.stop="handleQuery"
            >查询
          </el-button>
          <el-button icon="el-icon-refresh" @click.stop="handleReset"
            >重置
          </el-button>
        </div>
      </template>
      <template #toolbar_buttons>
        <el-button
          type="primary"
          @click="handleBatchAdd"
          v-has-permi="['lifePay:organization:import']"
          >导入</el-button
        >
      </template>
      <template #download="{row, operationType}">
        <FileTable
          :fileList="row.fileList"
          :fileOptions="{ url: 'storePath', name: 'docName' }"
          showCheckbox
          ref="fileTable"
        ></FileTable>
      </template>
      <template #handlingFee="{ row }">
        <el-button
          type="text"
          @click="handlePreviewFile(row, 'handlingFee')"
          v-if="row.handlingFee"
        >
          {{ row.handlingFee }}
        </el-button>
        <span v-else>-</span>
      </template>
      <template #collectionReceipt="{ row }">
        <el-button
          type="text"
          @click="handlePreviewFile(row, 'collectionReceipt')"
          v-if="row.collectionReceipt"
        >
          {{ row.collectionReceipt }}
        </el-button>
        <span v-else>-</span>
      </template>
      <template #modalFooter="{ row, crudOperationType }">
        <div v-if="crudOperationType === 'download'">
          <el-button @click="handleClose">取消</el-button>
          <el-button @click="submitDownloadList" type="primary">下载</el-button>
        </div>
      </template>
    </BuseCrud>
    <BatchUpload
      @uploadSuccess="handleQuery"
      ref="batchUpload"
      title="批量导入机构信息"
      :uploadApi="uploadObj.api"
      :templateUrl="uploadObj.url"
      :extraData="uploadObj.extraData"
      :maxSize="0.01953"
      maxSizeText="20m"
    >
    </BatchUpload>
    <!-- 文件预览组件 -->
    <PreviewFiles
      v-if="showFilePreview"
      :url-list="previewFileList"
      :initial-index="0"
      :file-options="{ url: 'storePath', name: 'docName' }"
      :on-close="closeFilePreview"
    />
  </div>
</template>

<script>
import checkPermission from "@/utils/permission.js";
import api from "@/api/settlement/lifePay/organization/index.js";
import { initParams } from "@/utils/buse.js";
import exportMixin from "@/mixin/export.js";
import moment from "moment";
import BatchUpload from "@/components/BatchUpload/index.vue";
import { queryLog } from "@/api/common.js";
import Timeline from "@/components/Timeline/index.vue";
import FileTable from "@/components/PreviewFiles/fileTable.vue";
import PreviewFiles from "@/components/PreviewFiles/index.vue";
import { downLoadUrl2Blob } from "@/api/common.js";
import { fileDownLoad } from "@/utils/downLoad.js";
export default {
  name: "lifePayOrganization",
  components: { BatchUpload, Timeline, FileTable, PreviewFiles },
  mixins: [exportMixin],
  data() {
    return {
      uploadObj: {
        api: "/st/lifePay/institInfo/importExcel",
        url: "/charging-maintenance-ui/static/机构信息导入模板.xlsx",
        extraData: {},
      },
      tableTotal: 0,
      // 查询参数
      searchForm: {
        pageNum: 1,
        pageSize: 10,
      },
      visible: false,
      //buse参数-s
      tableProps: {
        border: true,
        align: "center",
        resizable: true,
        showOverflow: "tooltip",
        toolbarConfig: {
          custom: true,
          slots: {
            buttons: "toolbar_buttons",
          },
        },
        rowConfig: {
          keyField: "agingId",
          isCurrent: true,
        },
        checkboxConfig: {
          // checkRowKeys: selectRowsId,
          reserve: true,
        },
      },
      tableData: [],

      params: {},
      loading: false,
      pagerProps: {
        layouts: [
          "Total",
          "Sizes",
          "PrevPage",
          "JumpNumber",
          "NextPage",
          "FullJump",
        ],
        background: true,
        className: "pagination-container",
        pageSizes: [10, 20, 30, 50],
      },
      tablePage: {
        total: 0,
        currentPage: 1,
        pageSize: 10,
      },
      operationType: "upload",
      //buse参数-e

      businessTypeOptions: [],
      onlineStatusOptions: [],
      serviceProviderOptions: [],
      institutionNameOptions: [],

      // 文件预览相关状态
      showFilePreview: false,
      previewFileList: [],
    };
  },
  created() {
    this.params = {
      ...initParams(this.filterOptions.config),
    };
    // 获取业务类型字典
    this.getDicts("st_business_type").then((response) => {
      this.businessTypeOptions = response.data;
    });

    // 获取上线状态字典
    this.getDicts("st_online_status").then((response) => {
      this.onlineStatusOptions = response.data;
    });

    // 获取下拉列表数据
    // api.getDropLists().then((res) => {
    //   if (res.success) {
    //     // 处理下拉列表数据
    //     if (res.data.institutionName) {
    //       this.institutionNameOptions = res.data.institutionName.map(
    //         (item) => ({
    //           dictLabel: item,
    //           dictValue: item,
    //         })
    //       );
    //     }
    //     if (res.data.serviceProvider) {
    //       this.serviceProviderOptions = res.data.serviceProvider.map(
    //         (item) => ({
    //           dictLabel: item,
    //           dictValue: item,
    //         })
    //       );
    //     }
    //   }
    // });

    // this.loadData();
  },
  activated() {
    this.loadData();
  },
  methods: {
    checkPermission,
    handleClose() {
      this.$refs.crud.switchModalView(false);
    },
    async submitDownloadList() {
      const list = this.$refs.fileTable.getCheckboxRecords();
      if (list?.length === 0) {
        this.$message.warning("请勾选要下载的文件");
        return;
      }
      const downloadPromises = list.map(async (x) => {
        try {
          const res = await downLoadUrl2Blob({ fileUrl: x.storePath });
          if (res) {
            await fileDownLoad(res, x.docName);
          }
        } catch (error) {
          // 处理错误，例如显示提示信息
          console.error(`Error downloading file: ${x.docName}`, error);
        }
      });
      this.downloadLoading = true;
      try {
        // 等待所有文件下载完成
        await Promise.all(downloadPromises);
      } finally {
        // 无论成功还是失败，都将 downloadLoading 设为 false
        this.downloadLoading = false;
      }
    },

    handleBatchAdd() {
      this.$refs.batchUpload.open();
    },

    rowEdit(row) {
      this.operationType = "update";
      this.$refs.crud.switchModalView(true, "UPDATE", {
        ...initParams(this.modalConfig.formConfig),
        ...row,
      });
    },
    handleDownload(row) {
      let fileList = [];
      if (row.handlingFee && row.handlingFeeUrl) {
        fileList.push({
          storePath: row.handlingFeeUrl,
          docName: row.handlingFee,
        });
      }
      if (row.collectionReceipt && row.collectionReceiptUrl) {
        fileList.push({
          storePath: row.collectionReceiptUrl,
          docName: row.collectionReceipt,
        });
      }
      this.$refs.crud.switchModalView(true, "download", {
        fileList,
      });
    },
    handleUpload(row) {
      this.operationType = "upload";
      this.$refs.crud.switchModalView(true, "upload", {
        ...initParams(this.modalConfig.formConfig),
        id: row.id,
      });
    },
    handleExport() {
      //数据是双向绑定的，所以直接获取data中定义的params即可获取到所有筛选项的值
      let params = {
        ...this.params,
        // pageNum: this.tablePage.currentPage,
        // pageSize: this.tablePage.pageSize,
      };

      this.handleTimeRange(params);
      this.handleCommonExport(api.export, params);
    },
    //处理时间范围参数
    handleTimeRange(params) {
      // 机构信息页面暂无时间范围参数
    },
    async loadData() {
      //数据是双向绑定的，所以直接获取data中定义的params即可获取到所有筛选项的值
      let params = {
        ...this.params,
        pageNum: this.tablePage.currentPage,
        pageSize: this.tablePage.pageSize,
      };

      this.handleTimeRange(params);
      this.loading = true;
      const res = await api.list(params);
      this.loading = false;
      this.tableData = res.data;
      this.tablePage.total = res.total;
    },
    handleReset() {
      this.tablePage = {
        total: 0,
        currentPage: 1,
        pageSize: 10,
      };
      this.params = initParams(this.filterOptions.config);
      this.loadData();
    },
    handleQuery() {
      this.tablePage.currentPage = 1;
      this.loadData();
    },
    //弹窗确认按钮事件
    async modalConfirmHandler({ crudOperationType, ...formParams }) {
      let params = { ...formParams };
      console.log(crudOperationType, formParams, "提交");
      // crudOperationType:update
      if (crudOperationType === "update") {
        const res = await api.update(params);
        if (res?.code === "10000") {
          this.$message.success("提交成功");
          this.loadData();
        } else {
          return false;
        }
      } else if (crudOperationType === "upload") {
        console.log("上传文件", params);
        const { handlingFeeList = [], collectionReceiptList = [] } = params;
        params = {
          ...params,
          handlingFee: handlingFeeList[0]?.docName,
          handlingFeeUrl: handlingFeeList[0]?.storePath,
          collectionReceipt: collectionReceiptList[0]?.docName,
          collectionReceiptUrl: collectionReceiptList[0]?.storePath,
        };
        api.uploadFile(params).then((res) => {
          if (res.code == "10000") {
            this.$message.success("上传成功");
            this.loadData();
          } else {
            return false;
          }
        });
      }
    },
    deleteRowHandler(row) {
      this.$confirm("是否确认删除？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then((res) => {
        let params = {
          id: row.id,
        };
        api.delete(params).then((res) => {
          if (res?.code === "10000") {
            this.$message.success("删除成功");
            this.loadData();
          }
        });
      });
    },

    // 处理文件预览
    handlePreviewFile(row, fieldType) {
      const fileList = [];

      if (
        fieldType === "handlingFee" &&
        row.handlingFee &&
        row.handlingFeeUrl
      ) {
        fileList.push({
          storePath: row.handlingFeeUrl,
          docName: row.handlingFee,
        });
      } else if (
        fieldType === "collectionReceipt" &&
        row.collectionReceipt &&
        row.collectionReceiptUrl
      ) {
        fileList.push({
          storePath: row.collectionReceiptUrl,
          docName: row.collectionReceipt,
        });
      }

      if (fileList.length > 0) {
        this.previewFileList = fileList;
        this.showFilePreview = true;
      } else {
        this.$message.warning("暂无文件可预览");
      }
    },

    // 关闭文件预览
    closeFilePreview() {
      this.showFilePreview = false;
      this.previewFileList = [];
    },
  },
  computed: {
    tableColumn() {
      return [
        {
          field: "institutionName",
          title: "机构名称",
          width: 180,
        },
        {
          field: "institutionCode",
          title: "机构编码",
          width: 120,
        },
        {
          field: "billingInstitutionCode",
          title: "出账机构编码",
          width: 120,
        },
        {
          field: "settlementInstitutionName",
          title: "结算机构名称",
          width: 120,
        },
        {
          field: "settlementPid",
          title: "结算PID",
          width: 120,
        },
        {
          field: "businessType",
          title: "业务类型",
          width: 120,
        },
        {
          field: "collectionInstitution",
          title: "销账机构",
          width: 120,
        },
        {
          field: "primaryServiceProvider",
          title: "一级服务商",
          width: 120,
        },
        {
          field: "secondaryServiceProvider",
          title: "二级服务商",
          width: 120,
        },
        {
          field: "onlineStatus",
          title: "上线状态",
          width: 120,
        },
        {
          field: "auditStatus",
          title: "审核状态",
          width: 120,
        },
        {
          field: "goLiveTime",
          title: "上线时间",
          width: 120,
        },
        {
          field: "settlementRequirements",
          title: "结算单要求",
          width: 120,
        },
        {
          field: "collectionRequirements",
          title: "收据要求",
          width: 120,
        },
        {
          field: "handlingFee",
          title: "手续函",
          width: 120,
          slots: { default: "handlingFee" },
        },
        {
          field: "collectionReceipt",
          title: "收据函",
          width: 120,
          slots: { default: "collectionReceipt" },
        },
        {
          field: "alipayBd",
          title: "支付宝BD",
          width: 120,
        },
      ];
    },
    filterOptions() {
      return {
        showCount: 5, //默认显示筛选项的个数 多余的展开展示
        layout: "right",
        inline: true,
        labelWidth: "140px",
        //筛选控件配置
        config: [
          {
            field: "institutionName",
            element: "el-input",
            title: "机构名称",
          },
          {
            field: "businessType",
            title: "业务类型",
            element: "el-select",
            props: {
              options: this.businessTypeOptions,
              optionLabel: "dictLabel",
              optionValue: "dictValue",
              filterable: true,
            },
          },
          {
            field: "onlineStatus",
            title: "上线状态",
            element: "el-select",
            props: {
              options: this.onlineStatusOptions,
              optionLabel: "dictLabel",
              optionValue: "dictValue",
              filterable: true,
            },
          },
          {
            field: "serviceProvider",
            element: "el-select",
            title: "服务商",
            props: {
              options: this.serviceProviderOptions,
              optionLabel: "dictLabel",
              optionValue: "dictValue",
              filterable: true,
            },
          },
        ],
        params: this.params,
      };
    },

    modalConfig() {
      const form = {
        update: [
          {
            field: "settlementRequirements",
            title: "结算单要求",
            element: "el-input",
            attrs: {
              maxlength: 100,
              placeholder: "100个字符以内",
            },
          },
          {
            field: "collectionRequirements",
            title: "收据要求",
            element: "el-input",
            attrs: {
              maxlength: 100,
              placeholder: "100个字符以内",
            },
          },
        ],
        upload: [
          {
            field: "handlingFeeList",
            title: "上传手续函",
            element: "file-upload",
            props: {
              showPaste: false,
              limit: 1,
              accept: ".jpg, .jpeg, .png, .pdf, .doc, .docx",
              fileMaxSize: 20,
              textTip: "上传格式为jpg、jpeg、png、pdf、doc、docx文件，20M以内",
            },
          },
          {
            field: "collectionReceiptList",
            title: "上传收据函",
            element: "file-upload",
            props: {
              showPaste: false,
              limit: 1,
              accept: ".jpg, .jpeg, .png, .pdf, .doc, .docx",
              fileMaxSize: 20,
              textTip: "上传格式为jpg、jpeg、png、pdf、doc、docx文件，20M以内",
            },
          },
        ],
      };
      return {
        // modalFullScreen: true,
        viewBtn: false,
        submitBtn: false,
        okText: "确认",
        cancelText: "取消",
        addBtn: false,
        editBtn: checkPermission(["lifePay:organization:edit"]),
        delBtn: false,
        menu: true,
        menuWidth: 250,
        menuFixed: "right",
        modalWidth: "50%",
        formConfig: form[this.operationType],
        crudPermission: [],
        customOperationTypes: [
          {
            title: "上传",
            typeName: "upload",
            event: (row) => {
              return this.handleUpload(row);
            },
            condition: (row) => {
              return checkPermission(["lifePay:organization:upload"]);
            },
          },
          {
            title: "下载",
            typeName: "download",
            slotName: "download",
            showForm: false,
            event: (row) => {
              return this.handleDownload(row);
            },
            condition: (row) => {
              return checkPermission(["lifePay:organization:download"]);
            },
          },
        ],
        formLayoutConfig: {
          defaultColSpan: 24,
          labelPosition: "right",
          labelWidth: "110px",
        },
      };
    },
  },
};
</script>

<style></style>
