<!-- 新大陆充电结算管理 -->
<template>
  <div class="card-container">
    <el-radio-group
      v-model="tabActiveTab"
      style="margin-bottom: 20px;"
      size="medium"
    >
      <el-radio-button
        :label="item.value"
        v-for="item in realList"
        :key="item.value"
        >{{ item.label }}
      </el-radio-button>
    </el-radio-group>
    <ProfitProgressPage
      v-show="tabActiveTab === 'profitProgress'"
      ref="profitProgress"
      :searchParams="searchParams"
    ></ProfitProgressPage>
    <PurchaseProgressPage
      v-show="tabActiveTab === 'purchaseProgress'"
      ref="purchaseProgress"
      @jump="handleJump"
    ></PurchaseProgressPage>
    <StaffMaintenancePage
      v-show="tabActiveTab === 'staffMaintenance'"
      ref="staffMaintenance"
    ></StaffMaintenancePage>
  </div>
</template>

<script>
import checkPermission from "@/utils/permission.js";

import ProfitProgressPage from "./profitProgress.vue";
import PurchaseProgressPage from "./purchaseProgress.vue";
import StaffMaintenancePage from "./staffMaintenance.vue";

export default {
  name: "xdtCharge",
  components: {
    ProfitProgressPage,
    PurchaseProgressPage,
    StaffMaintenancePage,
  },
  data() {
    return {
      tabActiveTab: "profitProgress",
      topTabDict: [
        {
          value: "profitProgress",
          label: "分润进度",
          show: () => {
            return this.checkPermission(["xdtCharge:profitProgress:list"]);
          },
        },
        {
          value: "purchaseProgress",
          label: "购电进度",
          show: () => {
            return this.checkPermission(["xdtCharge:purchaseProgress:list"]);
          },
        },
        {
          value: "staffMaintenance",
          label: "结算人员与运营商维护",
          show: () => {
            return this.checkPermission(["xdtCharge:staffMaintenance:list"]);
          },
        },
      ],
      searchParams: {},
    };
  },

  watch: {
    tabActiveTab: {
      handler(val) {
        this.$refs[val]?.loadData();
      },
    },
  },
  created() {
    this.realList = this.topTabDict.filter((x) => !!x.show());
    this.tabActiveTab = this.realList[0]?.value || "";
  },
  activated() {
    this.$refs[this.tabActiveTab]?.loadData();
  },
  methods: {
    checkPermission,
    handleJump(params) {
      this.searchParams = params;
      this.$nextTick(() => {
        // 可以在这里处理标签页跳转逻辑
      });
    },
  },
};
</script>

<style lang="less" scoped>
.radio-tab /deep/ .el-radio-button--small .el-radio-button__inner {
  padding: 12px 36px;
}
</style>
