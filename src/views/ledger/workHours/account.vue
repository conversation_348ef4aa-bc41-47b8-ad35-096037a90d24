<!-- 工时核算 -->
<template>
  <div>
    <BuseCrud
      ref="crud"
      :loading="loading"
      :filterOptions="filterOptions"
      :tablePage="tablePage"
      :pagerProps="pagerProps"
      :tableColumn="tableColumn"
      :tableData="tableData"
      :tableProps="tableProps"
      :modalConfig="modalConfig"
      @modalConfirm="modalConfirmHandler"
      @loadData="loadData"
    >
      <template slot="filterCustomBtn">
        <div class="btn-wrap">
          <el-button
            type="primary"
            icon="el-icon-download"
            @click.stop="handleExport"
            v-has-permi="['workHours:account:export']"
            >导出
          </el-button>
          <el-button
            type="primary"
            icon="el-icon-search"
            @click.stop="handleQuery"
            >查询
          </el-button>
          <el-button icon="el-icon-refresh" @click.stop="handleReset"
            >重置
          </el-button>
        </div>
      </template>
      <template #workRangeTime>
        <el-row>
          <el-col :span="6">
            <el-select
              v-model="params.rangeType"
              @change="handleRangeTypeChange"
            >
              <el-option label="周" value="week"> </el-option>
              <el-option label="月" value="month"> </el-option>
              <el-option label="年" value="year"> </el-option>
            </el-select>
          </el-col>
          <el-col :span="15" style="margin-left: 10px;">
            <el-date-picker
              type="week"
              valueFormat="yyyy-MM-dd"
              format="yyyy 第 WW 周"
              :pickerOptions="{
                firstDayOfWeek: 1,
              }"
              size="small"
              v-model="params.workRangeTime"
              v-if="params.rangeType === 'week'"
              key="week"
              placeholder="选择周"
            ></el-date-picker>
            <el-date-picker
              type="monthrange"
              valueFormat="yyyy-MM-dd"
              range-separator="至"
              start-placeholder="开始月份"
              end-placeholder="结束月份"
              v-model="params.workRangeTime"
              v-else-if="params.rangeType === 'month'"
              key="month"
            ></el-date-picker>
            <el-date-picker
              type="year"
              valueFormat="yyyy-MM-dd"
              v-model="params.workRangeTime"
              v-else-if="params.rangeType === 'year'"
              key="year"
              placeholder="选择年"
            ></el-date-picker>
          </el-col>
        </el-row>
      </template>
    </BuseCrud>
    <el-drawer title="工时明细" :visible.sync="visible" size="70%">
      <el-tabs v-model="activeName" type="card">
        <el-tab-pane label="填报工时" name="fill">
          <div class="page-center">
            <div class="title">{{ detailData.userName || "" }}：</div>
            <div v-for="(item, index) in fillSummaryList" :key="index">
              {{ item.title }}：<span class="count">{{ item.value || 0 }}</span
              ><span class="unit">{{ item.unit }}</span>
            </div>
          </div>
          <vxe-grid
            resizable
            align="center"
            :columns="fillColumns"
            :data="fillTableData"
            show-overflow
          ></vxe-grid>
        </el-tab-pane>
        <el-tab-pane label="订单制工时" name="order">
          <div class="page-center">
            <div class="title">{{ detailData.userName || "" }}：</div>
            <div v-for="(item, index) in orderSummaryList" :key="index">
              {{ item.title }}：<span class="count">{{ item.value || 0 }}</span
              ><span class="unit">{{ item.unit }}</span>
            </div>
          </div>
          <vxe-grid
            resizable
            align="center"
            :columns="orderColumns"
            :data="orderTableData"
          ></vxe-grid>
        </el-tab-pane>
      </el-tabs>
    </el-drawer>
  </div>
</template>

<script>
import checkPermission from "@/utils/permission.js";
import api from "@/api/ledger/workHours/account.js";
import { initParams } from "@/utils/buse.js";
import exportMixin from "@/mixin/export.js";
import { listDept } from "@/api/common.js";
import moment from "moment";

export default {
  name: "ledgerList",
  components: {},
  mixins: [exportMixin],
  data() {
    return {
      orderColumns: [
        { title: "人员姓名", field: "userName", width: "15%" },
        { title: "业务类型", field: "oneBusinessTypeName", width: "15%" },
        { title: "工单类型", field: "orderTypeStr", width: "20%" },
        { title: "数量（个）", field: "totalCount", width: "10%" },
        { title: "工单总工时（h）", field: "totalOrderTime", width: "10%" },
        { title: "加单总工时（h）", field: "totalAddTime", width: "10%" },
        { title: "工时总和（h）", field: "totalTime", width: "10%" },
        { title: "工单价格（元）", field: "orderPrice", width: "10%" },
      ],
      orderTableData: [],
      fillColumns: [
        { title: "姓名", field: "userName", width: 100 },
        { title: "工号", field: "workNumber", width: 100 },
        { title: "工时来源", field: "workHourSource", width: 100 },
        { title: "部门名称", field: "deptName", width: 120 },
        { title: "项目类型", field: "projectType", width: 120 },
        { title: "项目编码", field: "projectCode", width: 120 },
        { title: "工时编码", field: "workHourCode", width: 160 },
        { title: "工时名称", field: "workHourName", width: 160 },
        { title: "工时归属", field: "workHourHome", width: 100 },
        { title: "项目利润中心", field: "projectProfitCenter", width: 120 },
        { title: "支持工时编码", field: "supportWorkHourCode", width: 160 },
        { title: "支持工时名称", field: "supportWorkHourName", width: 160 },
        { title: "工时类型", field: "workHourType", width: 120 },
        { title: "工时开始日期", field: "startTime", width: 150 },
        { title: "工时结束日期", field: "endTime", width: 150 },
        { title: "工作时长（h）", field: "workHour", width: 100 },
        { title: "工时备注", field: "remark", width: 100 },
        { title: "审批状态", field: "status", width: 100 },
      ],
      fillTableData: [],
      visible: false,
      activeName: "fill",
      //buse参数-s
      tableProps: {
        border: true,
        align: "center",
        resizable: true,
        showOverflow: "tooltip",
        toolbarConfig: {
          custom: true,
          //   slots: {
          //     buttons: "toolbar_buttons",
          //   },
        },
        rowConfig: {
          keyField: "agingId",
          isCurrent: true,
        },
        checkboxConfig: {
          // checkRowKeys: selectRowsId,
          reserve: true,
        },
      },
      tableData: [],

      params: {},
      loading: false,
      pagerProps: {
        layouts: [
          "Total",
          "Sizes",
          "PrevPage",
          "JumpNumber",
          "NextPage",
          "FullJump",
        ],
        background: true,
        className: "pagination-container",
        pageSizes: [10, 20, 30, 50],
      },
      tablePage: {
        total: 0,
        currentPage: 1,
        pageSize: 10,
      },
      operationType: "accept",
      //buse参数-e

      deptOptions: [],
      workBelongOptions: [],
      detailData: {},
      fillSumData: {},
      orderSumData: {},
    };
  },
  created() {
    this.params = {
      ...initParams(this.filterOptions.config),
    };
    // 下拉菜单类型，00工时归属，01对应业务类型
    api.getDropdownList({ dropDownType: "00" }).then((res) => {
      this.workBelongOptions = res.data?.map((x) => {
        return { value: x.name, label: x.name };
      });
    });
    this.getTreeselect();
    // this.loadData();
  },
  methods: {
    checkPermission,
    handleDetail(row) {
      this.visible = true;
      this.activeName = "fill";
      this.detailData = { ...row };
      this.getFillDetail();
      this.getOrderDetail();
    },
    getFillDetail() {
      const params = {
        userId: this.detailData.userId,
        workHourHome: this.detailData.workHourHome,
        startDay: this.detailData.startDay,
        endDay: this.detailData.endDay,
      };
      api.fillDetailList(params).then((res) => {
        this.fillTableData = res.data;
      });
      api.fillSumData(params).then((res) => {
        this.fillSumData = { ...res.data };
      });
    },
    getOrderDetail() {
      const params = {
        userId: this.detailData.userId,
        workHourHome: this.detailData.workHourHome,
        startDay: this.detailData.startDay,
        endDay: this.detailData.endDay,
      };
      api.orderDetailList(params).then((res) => {
        this.orderTableData = res.data;
      });
      api.orderSumData(params).then((res) => {
        this.orderSumData = { ...res.data };
      });
    },
    handleExport() {
      //数据是双向绑定的，所以直接获取data中定义的params即可获取到所有筛选项的值
      let params = {
        ...this.params,
        // pageNum: this.tablePage.currentPage,
        // pageSize: this.tablePage.pageSize,
      };

      this.handleTimeRange(params);
      this.handleCommonExport(api.export, params);
    },
    //处理时间范围参数
    handleTimeRange(params) {
      if (params.rangeType === "week") {
        params.startDay =
          moment(params.workRangeTime)
            .isoWeekday(1)
            .format("YYYY-MM-DD") + " 00:00:00";
        params.endDay =
          moment(params.workRangeTime)
            .isoWeekday(7)
            .format("YYYY-MM-DD") + " 23:59:59";
      } else if (params.rangeType === "month") {
        params.startDay =
          moment(params.workRangeTime[0])
            .startOf("month")
            .format("YYYY-MM-DD") + " 00:00:00";
        params.endDay =
          moment(params.workRangeTime[1])
            .endOf("month")
            .format("YYYY-MM-DD") + " 23:59:59";
      } else if (params.rangeType === "year") {
        params.startDay =
          moment(params.workRangeTime)
            .startOf("year")
            .format("YYYY-MM-DD") + " 00:00:00";
        params.endDay =
          moment(params.workRangeTime)
            .endOf("year")
            .format("YYYY-MM-DD") + " 23:59:59";
      }
      //   const arr = [
      //     {
      //       field: "completeTime",
      //       title: "完成日期",
      //       startFieldName: "activityStartDate",
      //       endFieldName: "activityEndDate",
      //     },
      //   ];
      //   arr.map((x) => {
      //     if (Array.isArray(params[x.field])) {
      //       params[x.startFieldName] = params[x.field][0] + " 00:00:00";
      //       params[x.endFieldName] = params[x.field][1] + " 23:59:59";
      //       delete params[x.field];
      //     }
      //   });
    },
    async loadData() {
      //数据是双向绑定的，所以直接获取data中定义的params即可获取到所有筛选项的值
      let params = {
        ...this.params,
        pageNum: this.tablePage.currentPage,
        pageSize: this.tablePage.pageSize,
      };

      this.handleTimeRange(params);
      console.log("loadData", params);
      this.loading = true;
      const res = await api.list(params);
      this.loading = false;
      this.tableData = res.data;
      this.tablePage.total = res.total;
    },
    handleReset() {
      this.tablePage = {
        total: 0,
        currentPage: 1,
        pageSize: 10,
      };
      this.params = initParams(this.filterOptions.config);
      this.loadData();
    },
    handleQuery() {
      this.tablePage.currentPage = 1;
      this.loadData();
    },
    //弹窗确认按钮事件
    async modalConfirmHandler({ crudOperationType, ...formParams }) {
      let params = { ...formParams };
      console.log(crudOperationType, formParams, "提交");
      // crudOperationType:accept/remark/cancel/activityType
      const res = await api[crudOperationType](params);
      if (res?.code === "10000") {
        this.$message.success("提交成功");
        this.loadData();
      } else {
        return false;
      }
    },

    getTreeselect() {
      listDept({}).then((response) => {
        this.deptOptions = this.flattenArray(response.data);
      });
    },
    flattenArray(arr) {
      let result = [];
      const flatten = (arr) => {
        arr?.forEach((item) => {
          result.push(item);
          if (item.children) {
            flatten(item.children);
          }
        });
      };
      flatten(arr);
      return result;
    },
    treeChange(arr) {
      return arr.map((item) => {
        if (item.children && item.children.length > 0) {
          this.treeChange(item.children);
        } else {
          delete item.children;
        }
        item.label = item.deptName;
        item.id = item.deptId;
        return item;
      });
    },
    handleRangeTypeChange() {
      this.params.workRangeTime = undefined;
    },
  },
  computed: {
    fillSummaryList() {
      return [
        {
          title: "填报工时",
          value: this.fillSumData?.allFillHour ?? 0,
          unit: "h",
        },
        {
          title: "审批通过工时",
          value: this.fillSumData?.passFillHour ?? 0,
          unit: "h",
        },
        {
          title: "工时周期",
          value:
            (this.fillSumData?.startDay
              ? moment(this.fillSumData?.startDay).format("YYYY-MM-DD")
              : "") +
            "~" +
            (this.fillSumData?.endDay
              ? moment(this.fillSumData?.endDay).format("YYYY-MM-DD")
              : ""),
          unit: "",
        },
      ];
    },
    orderSummaryList() {
      return [
        {
          title: "订单制总工时",
          value: this.orderSumData?.orderHour ?? 0,
          unit: "h",
        },
        {
          title: "工单数量",
          value: this.orderSumData?.orderCount ?? 0,
          unit: "个",
        },
        {
          title: "工时周期",
          value:
            (this.orderSumData?.startDay
              ? moment(this.orderSumData?.startDay).format("YYYY-MM-DD")
              : "") +
            "~" +
            (this.orderSumData?.endDay
              ? moment(this.orderSumData?.endDay).format("YYYY-MM-DD")
              : ""),
          unit: "",
        },
      ];
    },
    tableColumn() {
      return [
        {
          field: "userName",
          title: "姓名",
        },
        {
          field: "jobNum",
          title: "工号",
        },
        {
          field: "deptName",
          title: "部门名称",
        },
        {
          field: "workHourHome",
          title: "工时归属",
        },
        {
          field: "fillJobHour",
          title: "填报工时（h）",
        },
        {
          field: "orderJobHour",
          title: "订单制工时（h）",
        },
        {
          field: "redundancyJobHour",
          title: "人力冗余时间（h）",
          titlePrefix: {
            message: `人力冗余时间=填报工时-订单制工时，单位：h`,
            icon: "vxe-icon-question-circle-fill",
          },
        },
        {
          field: "differencePercent",
          title: "差额幅度（%）",
          titlePrefix: {
            message: `差额幅度=（人力冗余时间/订单制工时）*100%，单位：%，小数点后保留2位，四舍五入；差额会出现负数`,
            icon: "vxe-icon-question-circle-fill",
          },
        },
        {
          field: "differencePeople",
          title: "冗余人力",
          titlePrefix: {
            message: `冗余人力=人力冗余时间/8/21.75，小数点后保留4位，四舍五入；冗余人力会出现负数`,
            icon: "vxe-icon-question-circle-fill",
          },
        },
        {
          field: "workTimeTypeConfigName",
          title: "工时计算区间",
          formatter: ({ cellValue, row }) => {
            return (row.startDay || "") + "~" + (row.endDay || "");
          },
        },
      ];
    },
    filterOptions() {
      return {
        showCount: 5, //默认显示筛选项的个数 多余的展开展示
        layout: "right",
        inline: true,
        labelWidth: "140px",
        //筛选控件配置
        config: [
          {
            field: "userName",
            element: "el-input",
            title: "姓名",
            attrs: {
              placeholder: "姓名或工号查询",
            },
          },
          {
            field: "workHourHome",
            title: "工时归属",
            element: "el-select",
            props: {
              options: this.workBelongOptions,
              // optionLabel: "dictLabel", //自定义选项名
              // optionValue: "dictValue", //自定义选项值
              filterable: true,
            },
          },
          {
            field: "deptId",
            title: "部门名称",
            element: "el-select",
            props: {
              options: this.deptOptions,
              filterable: true,
              optionLabel: "deptName",
              optionValue: "deptId",
              defaultExpandLevel: 1,
            },
          },
          {
            field: "rangeType",
            title: "周期维度",
            show: false,
            defaultValue: "week",
          },
          {
            field: "workRangeTime",
            title: "工时周期",
            element: "slot",
            slotName: "workRangeTime",
            props: {},
            colSpan: { span: 16 },
            defaultValue: moment()
              .subtract(1, "week")
              .format("YYYY-MM-DD"),
            rules: [
              { required: true, message: "请选择工时周期", trigger: "change" },
            ],
          },
        ],
        params: this.params,
      };
    },

    modalConfig() {
      return {
        // modalFullScreen: true,
        viewBtn: false,
        submitBtn: false,
        okText: "确认",
        cancelText: "取消",
        addBtn: false,
        editBtn: false,
        delBtn: false,
        menu: true,
        menuWidth: 150,
        menuFixed: "right",
        modalWidth: "50%",
        formConfig: [],
        crudPermission: [],
        customOperationTypes: [
          {
            title: "明细",
            typeName: "detail",
            showForm: false,
            event: (row) => {
              return this.handleDetail(row);
            },
            condition: (row) => {
              return checkPermission(["workHours:account:detail"]);
            },
          },
        ],
        formLayoutConfig: {
          defaultColSpan: 24,
          labelPosition: "right",
          labelWidth: "110px",
        },
      };
    },
  },
};
</script>

<style></style>
