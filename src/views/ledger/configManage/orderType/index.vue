// 工单类型
<template>
  <div class="app-container">
    <div class="page-header">
      <h3>工单类型</h3>
    </div>
    <el-radio-group v-model="activeName" style="margin:10px 0 20px;">
      <el-radio-button
        v-for="(item, index) in radioList"
        :key="index"
        :label="item.dictValue"
        >{{ item.dictLabel }}</el-radio-button
      >
    </el-radio-group>
    <el-row class="mb20">
      <el-button
        size="mini"
        type="primary"
        @click.stop="handleAdd(1)"
        v-has-permi="['ledger:workOrderType:add']"
        >新增</el-button
      >
      <el-button
        size="mini"
        type="primary"
        @click.stop="handleExport"
        v-has-permi="['ledger:workOrderType:export']"
        >导出</el-button
      >
      <el-button size="mini" type="primary" @click.stop="handleBatchMark"
        >批量打标</el-button
      >
      <el-button
        size="mini"
        type="primary"
        @click.stop="handleBatchConfigProcess"
        >批量配置流程</el-button
      >
    </el-row>
    <el-card v-loading="loading">
      <el-tree
        :data="data"
        node-key="id"
        default-expand-all
        :expand-on-click-node="false"
        :props="defaultProps"
        :draggable="true"
        :allow-drop="allowDrop"
        @node-drop="handleDrop"
        show-checkbox
        :check-strictly="false"
        @check="handleCheck"
        check-on-click-node
        ref="tree"
      >
        <div class="custom-tree-node" slot-scope="{ node, data }">
          <div class="node-label">{{ node.label }}</div>
          <!-- <div
            v-if="data.typeLevel == 2 || data.typeLevel == 3"
            class="node-middle"
          >
            <div>¥{{ showNum(data.orderQuotation) }}</div>
            <div>处理总时效：{{ showNum(data.handleTotalValidTime) }}h</div>
          </div> -->
          <div @click.stop v-if="data.typeLevel == 1">
            <el-switch
              v-model="data.orderAttr"
              active-text="标准工单"
              inactive-text="非标准工单"
              active-value="01"
              inactive-value="02"
              @change="(val) => handleStatusChange(data)"
            ></el-switch>
          </div>

          <div class="node-btn">
            <div class="mark-type">
              {{
                data.orderMark == "00"
                  ? "非订单制"
                  : data.orderMark == "01"
                  ? "订单制"
                  : ""
              }}
            </div>
            <el-button
              type="text"
              size="mini"
              @click.stop="handleNodeTime(data)"
              :style="data.typeLevel <= 1 ? 'visibility: hidden' : ''"
              :disabled="data.configProcessTag == 0"
              v-has-permi="['ledger:workOrderType:nodeTime']"
            >
              工序总工时：{{ showNum(data.totalJobHour) }}h
            </el-button>
            <el-button
              type="text"
              size="mini"
              @click.stop="handleDrawer(data)"
              :style="data.typeLevel <= 1 ? 'visibility: hidden' : ''"
              :disabled="data.configProcessTag == 0"
              v-has-permi="['ledger:workOrderType:process']"
              style="margin-right: 16px;"
            >
              {{ data.configProcessTag == 1 ? "已配置流程" : "未配置流程" }}
            </el-button>
            <el-button
              type="text"
              size="mini"
              @click.stop="handleAdd(data.typeLevel + 1, data)"
              :style="data.typeLevel > 2 ? 'visibility: hidden' : ''"
              icon="el-icon-plus"
              v-has-permi="['ledger:workOrderType:add']"
            >
              新增
            </el-button>
            <el-button
              type="text"
              size="mini"
              @click.stop="handleEdit(data.typeLevel, data)"
              icon="el-icon-edit"
              v-has-permi="['ledger:workOrderType:edit']"
            >
              修改
            </el-button>
            <el-button
              type="text"
              size="mini"
              @click.stop="handleDelete(data.id)"
              icon="el-icon-minus"
              style="color: red"
              v-has-permi="['ledger:workOrderType:delete']"
            >
              删除
            </el-button>
          </div>
        </div>
      </el-tree>
    </el-card>
    <el-dialog
      :title="title"
      :visible.sync="modalVisible"
      @close="closeDialog"
      :close-on-click-modal="false"
      width="50%"
      append-to-body
    >
      <el-form
        :model="modalForm"
        ref="modalForm"
        label-width="130px"
        :rules="rules"
      >
        <el-form-item label="一级类型：" prop="firstType">
          <span v-if="addLevel == 2 || addLevel == 3">{{
            modalForm.firstType
          }}</span>
          <el-input
            v-model="modalForm.firstType"
            placeholder="请输入一级工单类型，长度100个字符以内"
            maxlength="100"
            v-else
          />
        </el-form-item>
        <el-form-item
          label="二级类型："
          prop="secondType"
          v-if="addLevel == 2 || addLevel == 3"
        >
          <span v-if="addLevel == 3">{{ modalForm.secondType }}</span>
          <el-input
            v-model="modalForm.secondType"
            placeholder="请输入二级工单类型，长度100个字符以内"
            maxlength="100"
            v-else
          />
        </el-form-item>
        <el-form-item label="三级类型：" prop="thirdType" v-if="addLevel == 3">
          <el-input
            v-model="modalForm.thirdType"
            placeholder="请输入三级工单类型，长度100个字符以内"
            maxlength="100"
          />
        </el-form-item>

        <el-form-item
          label="配置流程："
          prop="flowKey"
          v-if="addLevel == 2 || addLevel == 3"
        >
          <el-row>
            <el-col :span="17">
              <el-select
                v-model="modalForm.flowKey"
                placeholder="请选择流程名称，单选，非必填"
                clearable
                style="width: 100%;"
                filterable
              >
                <el-option
                  v-for="item in processOptions"
                  :key="item.flowKey"
                  :label="item.flowName"
                  :value="item.flowKey"
                ></el-option>
              </el-select>
            </el-col>
            <el-col :span="6" :offset="1">
              <el-button @click="handleDrawer" plain type="primary"
                >配置新流程</el-button
              >
            </el-col>
          </el-row>
        </el-form-item>
        <el-row>
          <el-form-item
            prop="handleTotalValidTime"
            v-if="addLevel == 2 || addLevel == 3"
          >
            <div slot="label">
              <el-tooltip
                class="item"
                effect="dark"
                content="指的是该工单类型在流程节点以外的标准时效，比如设置为8小时，表示从创建工单为开始时间，8小时内必须处理完成，超过8小时未处理完成，该工单将开始记录超时时长；工单处理完成后，停止计算超时时长。"
                placement="top-start"
              >
                <i class="el-icon-question"></i>
              </el-tooltip>
              处理时效：
            </div>
            <el-col :span="21">
              <el-input-number
                v-model="modalForm.handleTotalValidTime"
                placeholder="请输入处理总时效"
                :controls="false"
                :max="999999.99"
                :precision="2"
                style="width: 100%;"
              >
              </el-input-number>
            </el-col>
            <el-col :span="2" :offset="1">h</el-col>
          </el-form-item>
        </el-row>
        <!-- <el-row>
          <el-form-item
            label="工单报价："
            prop="orderQuotation"
            v-if="addLevel == 2 || addLevel == 3"
          >
            <el-col :span="21">
              <el-input-number
                v-model="modalForm.orderQuotation"
                placeholder="请输入工单报价"
                :controls="false"
                :max="999999.99"
                :precision="2"
                style="width: 100%;"
              >
              </el-input-number>
            </el-col>
            <el-col :span="2" :offset="1">元</el-col>
          </el-form-item>
        </el-row> -->
        <el-row>
          <el-form-item
            prop="totalJobHour"
            v-if="addLevel == 2 || addLevel == 3"
          >
            <div slot="label">
              <el-tooltip
                class="item"
                effect="dark"
                content="工序总工时=每个节点工序工时之和"
                placement="top-start"
              >
                <i class="el-icon-question"></i>
              </el-tooltip>
              工序总工时：
            </div>
            <el-col>
              <span>{{ calTotalJobHour }}h</span>
            </el-col>
          </el-form-item>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click.stop="closeDialog">取 消</el-button>
        <el-button
          type="primary"
          @click.stop="handleSubmit"
          :loading="submitLoading"
          >提交</el-button
        >
      </div>
    </el-dialog>
    <el-dialog
      title="节点工时"
      :visible.sync="timeVisible"
      @close="timeVisible = false"
      :close-on-click-modal="false"
      width="50%"
      append-to-body
    >
      <div class="node-time-title">
        <div>工单类型：{{ orderTypeName }}</div>
        <div>工序总工时：{{ totalJobHour }}h</div>
      </div>
      <vxe-grid
        :columns="timeColumns"
        :data="timeTableData"
        resizable
        align="center"
      ></vxe-grid>
    </el-dialog>
    <el-drawer
      title="配置流程"
      :visible.sync="drawerVisible"
      size="80%"
      v-if="drawerVisible"
    >
      <Process :drawerFlowKey="flowKey" @close="handleClose"></Process>
    </el-drawer>
    <el-dialog
      title="批量打标"
      :visible.sync="batchMarkVisible"
      @close="batchMarkVisible = false"
      :close-on-click-modal="false"
      width="50%"
      append-to-body
    >
      <el-form :model="batchMarkForm" ref="batchMarkForm" label-width="100px">
        <el-form-item label="打标类型" prop="markType">
          <el-radio-group v-model="batchMarkForm.markType">
            <el-radio label="01">订单制</el-radio>
            <el-radio label="00">非订单制</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="batchMarkVisible = false">取 消</el-button>
        <el-button
          type="primary"
          @click="submitBatchMark"
          :loading="submitLoading"
          >确 定</el-button
        >
      </div>
    </el-dialog>

    <!-- 批量配置流程弹窗 -->
    <el-dialog
      title="批量配置流程"
      :visible.sync="batchConfigVisible"
      @close="batchConfigVisible = false"
      :close-on-click-modal="false"
      width="50%"
      append-to-body
    >
      <el-form
        :model="batchConfigForm"
        ref="batchConfigForm"
        label-width="130px"
        :rules="batchConfigRules"
      >
        <el-form-item label="配置流程：" prop="flowKey">
          <el-row>
            <el-col :span="17">
              <el-select
                v-model="batchConfigForm.flowKey"
                placeholder="请选择流程名称，单选，非必填"
                clearable
                style="width: 100%;"
                filterable
              >
                <el-option
                  v-for="item in processOptions"
                  :key="item.flowKey"
                  :label="item.flowName"
                  :value="item.flowKey"
                ></el-option>
              </el-select>
            </el-col>
            <el-col :span="6" :offset="1">
              <el-button @click="handleDrawer" plain type="primary"
                >配置新流程</el-button
              >
            </el-col>
          </el-row>
        </el-form-item>
        <el-row>
          <el-form-item prop="handleTotalValidTime">
            <div slot="label">
              <el-tooltip
                class="item"
                effect="dark"
                content="指的是该工单类型在流程节点以外的标准时效，比如设置为8小时，表示从创建工单为开始时间，8小时内必须处理完成，超过8小时未处理完成，该工单将开始记录超时时长；工单处理完成后，停止计算超时时长。"
                placement="top-start"
              >
                <i class="el-icon-question"></i>
              </el-tooltip>
              处理时效：
            </div>
            <el-col :span="21">
              <el-input-number
                v-model="batchConfigForm.handleTotalValidTime"
                placeholder="请输入处理总时效"
                :controls="false"
                :max="999999.99"
                :precision="2"
                style="width: 100%;"
              >
              </el-input-number>
            </el-col>
            <el-col :span="2" :offset="1">h</el-col>
          </el-form-item>
        </el-row>
        <el-row>
          <el-form-item prop="totalJobHour">
            <div slot="label">
              <el-tooltip
                class="item"
                effect="dark"
                content="工序总工时=每个节点工序工时之和"
                placement="top-start"
              >
                <i class="el-icon-question"></i>
              </el-tooltip>
              工序总工时：
            </div>
            <el-col>
              <span>{{ calAllTotalJobHour }}h</span>
            </el-col>
          </el-form-item>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="batchConfigVisible = false">取 消</el-button>
        <el-button
          type="primary"
          @click="submitBatchConfig"
          :loading="submitLoading"
          >确 定</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>

<script>
import * as api from "@/api/ledger/workOrderType.js";
import exportMixin from "@/mixin/export.js";
import Process from "@/views/ledger/configManage/process/index.vue";
import { createFlowKey } from "@/api/ledger/process.js";
export default {
  components: { Process },
  mixins: [exportMixin],
  data() {
    return {
      flowKey: "",
      drawerVisible: false,
      processOptions: [],
      orderTypeName: "",
      totalJobHour: "",
      timeColumns: [
        { title: "节点名称", field: "taskDefinitionName" },
        { title: "工序工时（h）", field: "handleAging" },
        // { title: "即将超时提醒时效（h）", field: "overRemindAging" },
      ],
      timeTableData: [],
      timeVisible: false,
      activeName: "1",
      modalVisible: false,
      title: "新增工单类型",
      submitLoading: false,
      data: [],
      modalForm: {
        firstType: "",
        secondType: "",
        thirdType: "",
        handleTotalValidTime: "",
        orderQuotation: "",
        flowKey: "",
        totalJobHour: "",
      },
      rules: {
        firstType: [{ required: true, message: "工单类型不能为空" }],
        secondType: [{ required: true, message: "工单类型不能为空" }],
        thirdType: [{ required: true, message: "工单类型不能为空" }],
        // handleTotalValidTime: [
        //   {
        //     pattern: /^(0|[1-9]\d{0,5}|999999)(\.\d{1,2})?$/,
        //     message: "请输入正确的数字",
        //   },
        // ],
        // orderQuotation: [
        //   {
        //     pattern: /^(0|[1-9]\d{0,5}|999999)(\.\d{1,2})?$/,
        //     message: "请输入正确的数字",
        //   },
        // ],
      },
      addLevel: 1,
      defaultProps: {
        children: "childrenList",
        label: "typeName",
      },
      flattenData: [],
      modalType: "add",
      parentId: undefined,
      editId: undefined,
      radioList: [
        // { label: "充电业务", value: "1" },
        // { label: "储能业务", value: "2" },
        // { label: "光伏业务", value: "3" },
      ],
      loading: false,
      batchMarkVisible: false,
      batchMarkForm: {
        markType: "01", // 默认选中订单制
      },
      batchConfigVisible: false,
      batchConfigForm: {
        flowKey: "",
        handleTotalValidTime: "",
      },
      batchConfigRules: {
        handleTotalValidTime: [
          {
            pattern: /^(0|[1-9]\d{0,5}|999999)(\.\d{1,2})?$/,
            message: "请输入正确的数字",
          },
        ],
      },
      checkedNodes: [], // 存储选中的节点
    };
  },
  mounted() {
    this.getProcessOptions();
    Promise.all([
      this.getDicts("support_dept").then((response) => {
        this.radioList = response.data;
        this.activeName = this.radioList[0]?.dictValue;
      }),
    ]).then(() => {
      setTimeout(() => {
        this.$nextTick(() => {
          this.getTreeData();
        });
      }, 500);
    });
  },
  watch: {
    activeName: {
      handler() {
        this.checkedNodes = [];
        this.getTreeData();
      },
    },
  },
  computed: {
    calTotalJobHour() {
      return this.processOptions?.find(
        (x) => x.flowKey === this.modalForm.flowKey
      )?.totalJobHour;
    },
    calAllTotalJobHour() {
      return this.processOptions?.find(
        (x) => x.flowKey === this.batchConfigForm.flowKey
      )?.totalJobHour;
    },
  },
  methods: {
    //状态切换
    async handleStatusChange(data) {
      const res = await api
        .changeOrderAttr({
          id: data.id,
          orderAttr: data.orderAttr,
          typeName: data.typeName,
        })
        .catch(() => {
          data.orderAttr = data.orderAttr == "01" ? "02" : "01";
        });
      if (res.code === "10000") {
        this.msgSuccess("操作成功");
      } else {
        this.msgError("操作失败");
        data.orderAttr = data.orderAttr == "01" ? "02" : "01";
      }
    },
    showNum(data) {
      return data ?? " - ";
    },
    getProcessOptions() {
      api.queryProcessOptions({ pageSize: 99999, pageNum: 1 }).then((res) => {
        if (res?.code === "10000") {
          this.processOptions = res.data;
        }
      });
    },
    handleNodeTime(data) {
      this.timeVisible = true;
      this.orderTypeName = data.typeName;
      this.totalJobHour = data.totalJobHour;
      api
        .queryNodeTimeList({ flowKey: data.flowKey, id: data.id })
        .then((res) => {
          if (res.code == "10000") {
            this.timeTableData = res.data?.nodeInfoList;
          } else {
            this.timeTableData = [];
          }
        })
        .catch(() => {
          this.timeTableData = [];
        });
    },
    handleDrawer(data = {}) {
      if (data.flowKey) {
        this.flowKey = data.flowKey;
        this.drawerVisible = true;
      } else {
        createFlowKey().then((res) => {
          this.flowKey = res.data;
          this.drawerVisible = true;
        });
      }
    },
    handleClose() {
      this.drawerVisible = false;
      this.modalForm.flowKey = this.flowKey;
      setTimeout(() => {
        this.getProcessOptions();
      }, 500);
      this.getTreeData();
    },

    handleExport() {
      const params = { supportDepts: [this.activeName] };
      this.handleCommonExport(api.exportApi, params);
    },
    flattenArray(arr) {
      let result = [];
      const flatten = (arr) => {
        arr.forEach((item) => {
          result.push(item);
          if (item.childrenList) {
            flatten(item.childrenList);
          }
          delete item.childrenList; // 删除childrenList属性
        });
      };
      flatten(arr);
      return result;
    },
    getTreeData() {
      this.loading = true;
      api
        .queryTreeList({ supportDepts: [this.activeName] })
        .then((res) => {
          this.loading = false;
          this.data = JSON.parse(JSON.stringify(res.data));
          this.flattenData = this.flattenArray(res.data);
        })
        .catch(() => {
          this.loading = false;
        });
    },
    handleAdd(level, data) {
      console.log(data);
      this.addLevel = level;
      this.modalVisible = true;
      this.title = "新增工单类型";
      this.modalType = "add";
      this.modalForm = {
        firstType: "",
        secondType: "",
        thirdType: "",
        handleTotalValidTime: "",
        orderQuotation: "",
        flowKey: "",
        totalJobHour: "",
      };
      if (level == 1) {
        this.modalForm.firstType = "";
        this.parentId = undefined;
      } else if (level == 2) {
        this.modalForm.firstType = data.typeName;
        this.modalForm.secondType = "";
        this.parentId = data.id;
      } else if (level == 3) {
        this.modalForm.firstType = data.parentName; //树结构需提供父级的label 或调接口获取
        this.modalForm.secondType = data.typeName;
        this.modalForm.thirdType = "";
        this.parentId = data.id;
      }
    },
    handleEdit(level, data) {
      this.addLevel = level;
      this.modalVisible = true;
      this.title = "修改工单类型";
      this.modalType = "edit";
      this.editId = data.id;
      this.modalForm = { ...this.modalForm, ...data };
      if (level == 1) {
        this.modalForm.firstType = data.typeName;
      } else if (level == 2) {
        this.modalForm.firstType = data.parentName;
        this.modalForm.secondType = data.typeName;
      } else if (level == 3) {
        this.modalForm.firstType = this.flattenData?.find(
          (x) => x.id == data.parentId
        )?.parentName; //树结构需提供父级的label 或调接口获取
        this.modalForm.secondType = data.parentName;
        this.modalForm.thirdType = data.typeName;
      }
    },
    //提交
    handleSubmit() {
      const arr = [
        { level: 1, value: "firstType" },
        { level: 2, value: "secondType" },
        { level: 3, value: "thirdType" },
      ];
      const method = this.modalType === "add" ? "addType" : "editType";
      const value = arr.find((x) => x.level === this.addLevel)?.value;

      this.$refs.modalForm.validate((valid) => {
        if (valid) {
          let params =
            this.modalType === "add"
              ? {
                  ...this.modalForm,
                  typeName: this.modalForm[value],
                  typeLevel: this.addLevel,
                  parentId: this.parentId,
                  supportDept: this.activeName,
                }
              : {
                  ...this.modalForm,
                  typeName: this.modalForm[value],
                  id: this.editId,
                };
          console.log("提交", this.modalForm);
          if (value === "firstType" && this.modalType === "add") {
            params.orderAttr = "01";
          }
          this.submitLoading = true;
          api[method](params)
            .then((res) => {
              if (res?.success) {
                this.$message.success("提交成功");
                this.getTreeData();
                this.closeDialog();
                this.submitLoading = false;
              }
            })
            .catch(() => {
              this.submitLoading = false;
            });
        } else {
          console.log("校验失败");
        }
      });
    },
    closeDialog() {
      console.log("关闭弹窗");
      this.$refs.modalForm.resetFields();
      this.modalVisible = false;
    },
    handleDelete(id) {
      const obj = {
        text: this.activeName == "5" ? "" : "删除后，已生成的工单不受影响！",
        title:
          this.activeName == "5"
            ? "删除该工单分类将影响客服工单系统数据同步，删除后该工单分类需要重新配置流程。"
            : "确定要删除该工单类型吗?",
      };
      this.$confirm(obj.text, obj.title, {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
        center: true,
      }).then(() => {
        let data = {
          id: id,
        };
        api.deleteType(data).then((res) => {
          if (res?.success) {
            this.$message.success("删除成功");
            //更新列表
            this.getTreeData();
          } else {
            this.$message.error("删除失败");
          }
        });
      });
    },
    allowDrop(draggingNode, dropNode, type) {
      if (draggingNode.level === dropNode.level) {
        if (draggingNode.data.parentId === dropNode.data.parentId) {
          return type === "prev" || type === "next";
        }
      } else {
        return false;
      }
    },
    handleDrop(draggingNode, dropNode) {
      // console.log(draggingNode, dropNode);
      let list = [];
      // 获取子级id
      for (let item of dropNode.parent.childNodes) {
        list.push(item.data.id);
      }
      // console.log(list, this.data, "========");
      api.sortTree({ ids: list }).then((res) => {
        if (res.code === "10000") {
          this.$message.success("排序成功");
          this.getTreeData();
        }
      });
    },
    handleCheck(data, checkedNodes) {
      console.log(data, checkedNodes);
      this.checkedNodes = checkedNodes.checkedNodes;
    },
    handleBatchMark() {
      if (this.checkedNodes.length === 0) {
        this.$message.warning("请先选择需要打标的工单类型");
        return;
      }
      this.batchMarkVisible = true;
    },

    // 批量配置流程
    handleBatchConfigProcess() {
      if (this.checkedNodes.length === 0) {
        this.$message.warning("请先选择需要配置流程的工单类型");
        return;
      }

      this.batchConfigVisible = true;
      this.batchConfigForm = {
        flowKey: "",
        handleTotalValidTime: "",
      };
    },

    // 提交批量配置流程
    submitBatchConfig() {
      this.$refs.batchConfigForm.validate((valid) => {
        if (valid) {
          if (!this.batchConfigForm.flowKey) {
            this.$message.warning("请选择配置流程");
            return;
          }

          // 过滤出二级和三级工单类型节点
          const validNodes = this.checkedNodes.filter(
            (node) => node.typeLevel === 2 || node.typeLevel === 3
          );
          const idList = validNodes.map((node) => node.id);

          this.submitLoading = true;

          api
            .batchConfigProcess({
              ids: idList,
              handleTotalValidTime: this.batchConfigForm.handleTotalValidTime,
              flowKey: this.batchConfigForm.flowKey,
            })
            .then((res) => {
              if (res?.code === "10000") {
                this.$message.success("批量配置流程成功");
                this.batchConfigVisible = false;
                this.getTreeData();
              } else {
                this.$message.error(res?.msg || "批量配置流程失败");
              }
            })
            .finally(() => {
              this.submitLoading = false;
            });
        }
      });
    },
    submitBatchMark() {
      if (!this.batchMarkForm.markType) {
        this.$message.warning("请选择打标类型");
        return;
      }

      const idList = this.checkedNodes?.map((node) => node.id);

      this.submitLoading = true;
      api
        .batchMark({
          idList,
          orderMark: this.batchMarkForm.markType,
        })
        .then((res) => {
          if (res?.code === "10000") {
            this.$message.success("批量打标成功");
            this.batchMarkVisible = false;
            this.getTreeData();
          } else {
            this.$message.error(res?.msg || "批量打标失败");
          }
        })
        .finally(() => {
          this.submitLoading = false;
        });
    },
  },
};
</script>

<style lang="less" scoped>
.page-header {
  display: flex;
  align-items: center;
  h3 {
    margin-right: 40px;
  }
}
.custom-tree-node {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  padding-right: 8px;
  .node-label {
    width: 200px;
    word-wrap: break-word;
    white-space: normal;
  }
  .node-btn {
    min-width: 170px;
    display: flex;
    justify-content: flex-end;
    align-items: center;
  }
  .node-middle {
    display: flex;
    flex-wrap: wrap;
    flex: 1;
    font-size: 12px;
    color: #5a5959;
    div {
      margin-right: 30px;
      padding: 10px;
    }
  }
  .mark-type {
    min-width: 80px;
    text-align: center;
    margin-right: 40px;
    //  color: #409eff;
  }
}
/deep/ .el-tree-node__content {
  padding-top: 10px;
  padding-bottom: 10px;
  height: auto;
}
/deep/ .el-card__body {
  max-height: 80vh;
  overflow-y: auto;
}
/deep/ .el-radio-button--small .el-radio-button__inner {
  padding: 10px 24px;
  font-size: 14px;
}
.node-time-title {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
}
</style>
