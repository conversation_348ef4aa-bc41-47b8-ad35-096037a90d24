import request from "@/utils/request";

/**
 * 新电途充电分润进度管理API接口
 * 提供分润进度信息的增删改查功能
 */
export default {
  /**
   * 分页查询分润进度信息
   * @param {Object} data - 查询参数
   * @param {number} [data.pageNum=1] - 页码
   * @param {number} [data.pageSize=10] - 每页条数
   * @param {number} [data.tenantId] - 租户ID
   * @param {number} [data.orgNo] - 机构编号
   * @param {Array<number>} [data.orgNoList] - 机构编号列表
   * @param {number} [data.operatorId] - 操作员ID
   * @param {string} [data.operatorName] - 操作员名称
   * @param {string} [data.operator] - 运营商（模糊查询）
   * @param {string} [data.reconciliationPerson] - 对账人（模糊查询）
   * @param {string} [data.ourSeal] - 我方盖章状态
   * @param {string} [data.billReview] - 账单审核状态
   * @param {string} [data.counterpartySeal] - 对方盖章状态
   * @param {string} [data.returnComplete] - 退回完成状态
   * @param {string} [data.billYearMonthStart] - 账单年月开始
   * @param {string} [data.billYearMonthEnd] - 账单年月结束
   * @returns {Promise<Object>} 返回分页查询结果
   * @returns {boolean} returns.success - 请求是否成功
   * @returns {string} returns.code - 响应状态码
   * @returns {string} returns.message - 响应消息
   * @returns {Array<Object>} returns.data - 分润进度信息列表
   * @returns {number} returns.data[].id - 记录ID
   * @returns {string} returns.data[].billYearMonth - 账单年月
   * @returns {string} returns.data[].operator - 运营商
   * @returns {string} returns.data[].costType - 费用类型
   * @returns {number} returns.data[].sharedIncome - 分润收入
   * @returns {number} returns.data[].returnAmount - 退回金额
   * @returns {string} returns.data[].reconciliationPerson - 对账人
   * @returns {string} returns.data[].billReview - 账单审核
   * @returns {string} returns.data[].counterpartySeal - 对方盖章
   * @returns {string} returns.data[].ourSeal - 我方盖章
   * @returns {string} returns.data[].returnComplete - 退回完成
   * @returns {string} returns.data[].remarks - 备注
   * @returns {string} returns.data[].mode - 模式
   * @returns {string} returns.data[].createBy - 创建人
   * @returns {string} returns.data[].createTime - 创建时间
   * @returns {string} returns.data[].updateBy - 更新人
   * @returns {string} returns.data[].updateTime - 更新时间
   * @returns {number} returns.pageNum - 当前页码
   * @returns {number} returns.pageSize - 每页条数
   * @returns {number} returns.total - 总记录数
   * @returns {string} returns.traceId - 请求追踪ID
   * @example
   * // 查询第一页数据
   * const result = await profitProgressApi.list({
   *   pageNum: 1,
   *   pageSize: 10,
   *   operator: '运营商名称'
   * });
   */
  list(data) {
    return request({
      url: "/st/newcharge/profit/queryPage",
      method: "post",
      data: data,
    });
  },

  /**
   * 编辑分润进度信息
   * @param {Object} data - 分润进度信息数据
   * @param {number} data.id - 分润进度信息ID（必填）
   * @param {string} [data.billYearMonth] - 账单年月
   * @param {string} [data.operator] - 运营商
   * @param {string} [data.costType] - 费用类型
   * @param {number} [data.sharedIncome] - 分润收入
   * @param {number} [data.returnAmount] - 退回金额
   * @param {string} [data.reconciliationPerson] - 对账人
   * @param {string} [data.billReview] - 账单审核
   * @param {string} [data.counterpartySeal] - 对方盖章
   * @param {string} [data.ourSeal] - 我方盖章
   * @param {string} [data.returnComplete] - 退回完成
   * @param {string} [data.remarks] - 备注
   * @param {string} [data.mode] - 模式
   * @returns {Promise<Object>} 返回编辑结果
   * @returns {boolean} returns.success - 请求是否成功
   * @returns {string} returns.code - 响应状态码
   * @returns {string} returns.message - 响应消息
   * @returns {string} returns.data - 操作结果信息
   * @returns {string} returns.traceId - 请求追踪ID
   * @example
   * // 编辑分润进度信息
   * const result = await profitProgressApi.update({
   *   id: 1,
   *   returnAmount: 950.00,
   *   billReview: '已审核'
   * });
   */
  update(data) {
    return request({
      url: "/st/newcharge/profit/edit",
      method: "post",
      data: data,
    });
  },
  //新增
  add(data) {
    return request({
      url: "/st/newcharge/profit/add",
      method: "post",
      data: data,
    });
  },

  /**
   * 删除分润进度信息
   * @param {Object} data - 删除参数
   * @param {number|Array<number>} data.id - 要删除的分润进度信息ID，支持单个ID或ID数组
   * @returns {Promise<Object>} 返回删除结果
   * @returns {boolean} returns.success - 请求是否成功
   * @returns {string} returns.code - 响应状态码
   * @returns {string} returns.message - 响应消息
   * @returns {string} returns.data - 删除结果信息
   * @returns {string} returns.traceId - 请求追踪ID
   * @example
   * // 删除单个分润进度信息
   * const result = await profitProgressApi.delete({ id: 1 });
   *
   * // 批量删除分润进度信息
   * const result = await profitProgressApi.delete({ id: [1, 2, 3] });
   */
  delete(data) {
    return request({
      url: "/st/newcharge/profit/delete",
      method: "post",
      data: data,
    });
  },

  /**
   * 导出分润进度信息Excel文件
   * @param {Object} data - 导出参数（与查询参数相同，用于筛选导出数据）
   * @param {string} [data.operator] - 运营商筛选条件
   * @param {string} [data.reconciliationPerson] - 对账人筛选条件
   * @param {string} [data.ourSeal] - 我方盖章状态筛选条件
   * @param {string} [data.billReview] - 账单审核状态筛选条件
   * @param {string} [data.counterpartySeal] - 对方盖章状态筛选条件
   * @param {string} [data.returnComplete] - 退回完成状态筛选条件
   * @param {string} [data.billYearMonthStart] - 账单年月开始筛选条件
   * @param {string} [data.billYearMonthEnd] - 账单年月结束筛选条件
   * @returns {Promise<Object>} 返回导出结果
   * @returns {boolean} returns.success - 请求是否成功
   * @returns {string} returns.code - 响应状态码
   * @returns {string} returns.message - 响应消息
   * @returns {string} returns.data - 导出文件下载链接或文件内容
   * @returns {string} returns.traceId - 请求追踪ID
   * @example
   * // 导出所有分润进度信息
   * const result = await profitProgressApi.export({
   *   operator: '运营商名称'
   * });
   */
  export(data) {
    return request({
      url: "/st/newcharge/profit/exportExcel",
      method: "post",
      data: data,
    });
  },

  /**
   * 导入分润进度信息Excel文件
   * @param {FormData} data - 包含文件的FormData对象
   * @param {File} data.file - 要导入的Excel文件（必填）
   * @returns {Promise<Object>} 返回导入结果
   * @returns {boolean} returns.success - 请求是否成功
   * @returns {string} returns.code - 响应状态码
   * @returns {string} returns.message - 响应消息（包含导入成功/失败信息）
   * @returns {string} returns.data - 导入结果详情
   * @returns {string} returns.traceId - 请求追踪ID
   * @example
   * // 导入Excel文件
   * const formData = new FormData();
   * formData.append('file', file);
   * const result = await profitProgressApi.import(formData);
   */
  import(data) {
    return request({
      url: "/st/newcharge/profit/importExcel",
      method: "post",
      data: data,
      headers: {
        "Content-Type": "multipart/form-data",
      },
      skipIntercept: true,
    });
  },

  /**
   * 获取分润进度相关的下拉列表数据
   * 用于表单选择器的数据源
   * @returns {Promise<Object>} 返回下拉列表数据
   * @returns {boolean} returns.success - 请求是否成功
   * @returns {string} returns.code - 响应状态码
   * @returns {string} returns.message - 响应消息
   * @returns {Object} returns.data - 下拉列表数据对象，key为字段名，value为选项数组
   * @returns {Array<string>} [returns.data.operators] - 运营商选项列表
   * @returns {Array<string>} [returns.data.reconciliationPersons] - 对账人选项列表
   * @returns {string} returns.traceId - 请求追踪ID
   * @example
   * // 获取下拉列表数据
   * const result = await profitProgressApi.getDropLists();
   * // result.data = {
   * //   operators: ['运营商A', '运营商B'],
   * //   reconciliationPersons: ['张三', '李四']
   * // }
   */
  getDropLists() {
    return request({
      url: "/st/newcharge/profit/getDropLists",
      method: "get",
    });
  },

  // 批量操作
  batchOperation(data) {
    return request({
      url: "/st/newcharge/profit/batchOperation",
      method: "post",
      data: data,
    });
  },
};
